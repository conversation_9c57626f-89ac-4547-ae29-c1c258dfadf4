# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: wv_proto4.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x0fwv_proto4.proto\"\xc5\x05\n\x14\x43lientIdentification\x12-\n\x04Type\x18\x01 \x01(\x0e\x32\x1f.ClientIdentification.TokenType\x12\'\n\x05Token\x18\x02 \x01(\x0b\x32\x18.SignedDeviceCertificate\x12\x33\n\nClientInfo\x18\x03 \x03(\x0b\x32\x1f.ClientIdentification.NameValue\x12\x1b\n\x13ProviderClientToken\x18\x04 \x01(\x0c\x12\x16\n\x0eLicenseCounter\x18\x05 \x01(\r\x12\x45\n\x13_ClientCapabilities\x18\x06 \x01(\x0b\x32(.ClientIdentification.ClientCapabilities\x1a(\n\tNameValue\x12\x0c\n\x04Name\x18\x01 \x01(\t\x12\r\n\x05Value\x18\x02 \x01(\t\x1a\xa4\x02\n\x12\x43lientCapabilities\x12\x13\n\x0b\x43lientToken\x18\x01 \x01(\r\x12\x14\n\x0cSessionToken\x18\x02 \x01(\r\x12\"\n\x1aVideoResolutionConstraints\x18\x03 \x01(\r\x12L\n\x0eMaxHdcpVersion\x18\x04 \x01(\x0e\x32\x34.ClientIdentification.ClientCapabilities.HdcpVersion\x12\x1b\n\x13OemCryptoApiVersion\x18\x05 \x01(\r\"T\n\x0bHdcpVersion\x12\r\n\tHDCP_NONE\x10\x00\x12\x0b\n\x07HDCP_V1\x10\x01\x12\x0b\n\x07HDCP_V2\x10\x02\x12\r\n\tHDCP_V2_1\x10\x03\x12\r\n\tHDCP_V2_2\x10\x04\"S\n\tTokenType\x12\n\n\x06KEYBOX\x10\x00\x12\x16\n\x12\x44\x45VICE_CERTIFICATE\x10\x01\x12\"\n\x1eREMOTE_ATTESTATION_CERTIFICATE\x10\x02\"\x9b\x02\n\x11\x44\x65viceCertificate\x12\x30\n\x04Type\x18\x01 \x01(\x0e\x32\".DeviceCertificate.CertificateType\x12\x14\n\x0cSerialNumber\x18\x02 \x01(\x0c\x12\x1b\n\x13\x43reationTimeSeconds\x18\x03 \x01(\r\x12\x11\n\tPublicKey\x18\x04 \x01(\x0c\x12\x10\n\x08SystemId\x18\x05 \x01(\r\x12\x1c\n\x14TestDeviceDeprecated\x18\x06 \x01(\r\x12\x11\n\tServiceId\x18\x07 \x01(\x0c\"K\n\x0f\x43\x65rtificateType\x12\x08\n\x04ROOT\x10\x00\x12\x10\n\x0cINTERMEDIATE\x10\x01\x12\x0f\n\x0bUSER_DEVICE\x10\x02\x12\x0b\n\x07SERVICE\x10\x03\"\xc4\x01\n\x17\x44\x65viceCertificateStatus\x12\x14\n\x0cSerialNumber\x18\x01 \x01(\x0c\x12:\n\x06Status\x18\x02 \x01(\x0e\x32*.DeviceCertificateStatus.CertificateStatus\x12*\n\nDeviceInfo\x18\x04 \x01(\x0b\x32\x16.ProvisionedDeviceInfo\"+\n\x11\x43\x65rtificateStatus\x12\t\n\x05VALID\x10\x00\x12\x0b\n\x07REVOKED\x10\x01\"o\n\x1b\x44\x65viceCertificateStatusList\x12\x1b\n\x13\x43reationTimeSeconds\x18\x01 \x01(\r\x12\x33\n\x11\x43\x65rtificateStatus\x18\x02 \x03(\x0b\x32\x18.DeviceCertificateStatus\"\xaf\x01\n\x1d\x45ncryptedClientIdentification\x12\x11\n\tServiceId\x18\x01 \x01(\t\x12&\n\x1eServiceCertificateSerialNumber\x18\x02 \x01(\x0c\x12\x19\n\x11\x45ncryptedClientId\x18\x03 \x01(\x0c\x12\x1b\n\x13\x45ncryptedClientIdIv\x18\x04 \x01(\x0c\x12\x1b\n\x13\x45ncryptedPrivacyKey\x18\x05 \x01(\x0c\"\x9c\x01\n\x15LicenseIdentification\x12\x11\n\tRequestId\x18\x01 \x01(\x0c\x12\x11\n\tSessionId\x18\x02 \x01(\x0c\x12\x12\n\nPurchaseId\x18\x03 \x01(\x0c\x12\x1a\n\x04Type\x18\x04 \x01(\x0e\x32\x0c.LicenseType\x12\x0f\n\x07Version\x18\x05 \x01(\r\x12\x1c\n\x14ProviderSessionToken\x18\x06 \x01(\x0c\"\xfa\x0e\n\x07License\x12\"\n\x02Id\x18\x01 \x01(\x0b\x32\x16.LicenseIdentification\x12 \n\x07_Policy\x18\x02 \x01(\x0b\x32\x0f.License.Policy\x12\"\n\x03Key\x18\x03 \x03(\x0b\x32\x15.License.KeyContainer\x12\x18\n\x10LicenseStartTime\x18\x04 \x01(\r\x12!\n\x19RemoteAttestationVerified\x18\x05 \x01(\r\x12\x1b\n\x13ProviderClientToken\x18\x06 \x01(\x0c\x12\x18\n\x10ProtectionScheme\x18\x07 \x01(\r\x12\x1c\n\x14UnknownHdcpDataField\x18\x08 \x01(\x0c\x1a\xd4\x02\n\x06Policy\x12\x0f\n\x07\x43\x61nPlay\x18\x01 \x01(\r\x12\x12\n\nCanPersist\x18\x02 \x01(\r\x12\x10\n\x08\x43\x61nRenew\x18\x03 \x01(\r\x12\x1d\n\x15RentalDurationSeconds\x18\x04 \x01(\r\x12\x1f\n\x17PlaybackDurationSeconds\x18\x05 \x01(\r\x12\x1e\n\x16LicenseDurationSeconds\x18\x06 \x01(\r\x12&\n\x1eRenewalRecoveryDurationSeconds\x18\x07 \x01(\r\x12\x18\n\x10RenewalServerUrl\x18\x08 \x01(\t\x12\x1b\n\x13RenewalDelaySeconds\x18\t \x01(\r\x12#\n\x1bRenewalRetryIntervalSeconds\x18\n \x01(\r\x12\x16\n\x0eRenewWithUsage\x18\x0b \x01(\r\x12\x17\n\x0fUnknownPolicy12\x18\x0c \x01(\r\x1a\x9b\n\n\x0cKeyContainer\x12\n\n\x02Id\x18\x01 \x01(\x0c\x12\n\n\x02Iv\x18\x02 \x01(\x0c\x12\x0b\n\x03Key\x18\x03 \x01(\x0c\x12+\n\x04Type\x18\x04 \x01(\x0e\x32\x1d.License.KeyContainer.KeyType\x12\x32\n\x05Level\x18\x05 \x01(\x0e\x32#.License.KeyContainer.SecurityLevel\x12\x42\n\x12RequiredProtection\x18\x06 \x01(\x0b\x32&.License.KeyContainer.OutputProtection\x12\x43\n\x13RequestedProtection\x18\x07 \x01(\x0b\x32&.License.KeyContainer.OutputProtection\x12\x35\n\x0b_KeyControl\x18\x08 \x01(\x0b\x32 .License.KeyContainer.KeyControl\x12[\n\x1e_OperatorSessionKeyPermissions\x18\t \x01(\x0b\x32\x33.License.KeyContainer.OperatorSessionKeyPermissions\x12S\n\x1aVideoResolutionConstraints\x18\n \x03(\x0b\x32/.License.KeyContainer.VideoResolutionConstraint\x1a\xdb\x01\n\x10OutputProtection\x12\x42\n\x04Hdcp\x18\x01 \x01(\x0e\x32\x34.ClientIdentification.ClientCapabilities.HdcpVersion\x12>\n\tCgmsFlags\x18\x02 \x01(\x0e\x32+.License.KeyContainer.OutputProtection.CGMS\"C\n\x04\x43GMS\x12\r\n\tCOPY_FREE\x10\x00\x12\r\n\tCOPY_ONCE\x10\x02\x12\x0e\n\nCOPY_NEVER\x10\x03\x12\r\n\tCGMS_NONE\x10*\x1a\x31\n\nKeyControl\x12\x17\n\x0fKeyControlBlock\x18\x01 \x01(\x0c\x12\n\n\x02Iv\x18\x02 \x01(\x0c\x1a|\n\x1dOperatorSessionKeyPermissions\x12\x14\n\x0c\x41llowEncrypt\x18\x01 \x01(\r\x12\x14\n\x0c\x41llowDecrypt\x18\x02 \x01(\r\x12\x11\n\tAllowSign\x18\x03 \x01(\r\x12\x1c\n\x14\x41llowSignatureVerify\x18\x04 \x01(\r\x1a\x99\x01\n\x19VideoResolutionConstraint\x12\x1b\n\x13MinResolutionPixels\x18\x01 \x01(\r\x12\x1b\n\x13MaxResolutionPixels\x18\x02 \x01(\r\x12\x42\n\x12RequiredProtection\x18\x03 \x01(\x0b\x32&.License.KeyContainer.OutputProtection\"Z\n\x07KeyType\x12\x0e\n\n_NOKEYTYPE\x10\x00\x12\x0b\n\x07SIGNING\x10\x01\x12\x0b\n\x07\x43ONTENT\x10\x02\x12\x0f\n\x0bKEY_CONTROL\x10\x03\x12\x14\n\x10OPERATOR_SESSION\x10\x04\"\x8b\x01\n\rSecurityLevel\x12\x0f\n\x0b_NOSECLEVEL\x10\x00\x12\x14\n\x10SW_SECURE_CRYPTO\x10\x01\x12\x14\n\x10SW_SECURE_DECODE\x10\x02\x12\x14\n\x10HW_SECURE_CRYPTO\x10\x03\x12\x14\n\x10HW_SECURE_DECODE\x10\x04\x12\x11\n\rHW_SECURE_ALL\x10\x05\"\xac\x01\n\x0cLicenseError\x12&\n\tErrorCode\x18\x01 \x01(\x0e\x32\x13.LicenseError.Error\"t\n\x05\x45rror\x12\x12\n\x0e\x44UMMY_NO_ERROR\x10\x00\x12\x1e\n\x1aINVALID_DEVICE_CERTIFICATE\x10\x01\x12\x1e\n\x1aREVOKED_DEVICE_CERTIFICATE\x10\x02\x12\x17\n\x13SERVICE_UNAVAILABLE\x10\x03\"\xc0\x07\n\x0eLicenseRequest\x12\'\n\x08\x43lientId\x18\x01 \x01(\x0b\x32\x15.ClientIdentification\x12\x38\n\tContentId\x18\x02 \x01(\x0b\x32%.LicenseRequest.ContentIdentification\x12)\n\x04Type\x18\x03 \x01(\x0e\x32\x1b.LicenseRequest.RequestType\x12\x13\n\x0bRequestTime\x18\x04 \x01(\r\x12!\n\x19KeyControlNonceDeprecated\x18\x05 \x01(\x0c\x12)\n\x0fProtocolVersion\x18\x06 \x01(\x0e\x32\x10.ProtocolVersion\x12\x17\n\x0fKeyControlNonce\x18\x07 \x01(\r\x12\x39\n\x11\x45ncryptedClientId\x18\x08 \x01(\x0b\x32\x1e.EncryptedClientIdentification\x1a\xa2\x04\n\x15\x43ontentIdentification\x12:\n\x06\x43\x65ncId\x18\x01 \x01(\x0b\x32*.LicenseRequest.ContentIdentification.CENC\x12:\n\x06WebmId\x18\x02 \x01(\x0b\x32*.LicenseRequest.ContentIdentification.WebM\x12\x46\n\x07License\x18\x03 \x01(\x0b\x32\x35.LicenseRequest.ContentIdentification.ExistingLicense\x1a_\n\x04\x43\x45NC\x12!\n\x04Pssh\x18\x01 \x01(\x0b\x32\x13.WidevineCencHeader\x12!\n\x0bLicenseType\x18\x02 \x01(\x0e\x32\x0c.LicenseType\x12\x11\n\tRequestId\x18\x03 \x01(\x0c\x1aL\n\x04WebM\x12\x0e\n\x06Header\x18\x01 \x01(\x0c\x12!\n\x0bLicenseType\x18\x02 \x01(\x0e\x32\x0c.LicenseType\x12\x11\n\tRequestId\x18\x03 \x01(\x0c\x1a\x99\x01\n\x0f\x45xistingLicense\x12)\n\tLicenseId\x18\x01 \x01(\x0b\x32\x16.LicenseIdentification\x12\x1b\n\x13SecondsSinceStarted\x18\x02 \x01(\r\x12\x1e\n\x16SecondsSinceLastPlayed\x18\x03 \x01(\r\x12\x1e\n\x16SessionUsageTableEntry\x18\x04 \x01(\x0c\"D\n\x0bRequestType\x12\x12\n\x0e\x44UMMY_REQ_TYPE\x10\x00\x12\x07\n\x03NEW\x10\x01\x12\x0b\n\x07RENEWAL\x10\x02\x12\x0b\n\x07RELEASE\x10\x03\"\xa6\x02\n\x15ProvisionedDeviceInfo\x12\x10\n\x08SystemId\x18\x01 \x01(\r\x12\x0b\n\x03Soc\x18\x02 \x01(\t\x12\x14\n\x0cManufacturer\x18\x03 \x01(\t\x12\r\n\x05Model\x18\x04 \x01(\t\x12\x12\n\nDeviceType\x18\x05 \x01(\t\x12\x11\n\tModelYear\x18\x06 \x01(\r\x12=\n\rSecurityLevel\x18\x07 \x01(\x0e\x32&.ProvisionedDeviceInfo.WvSecurityLevel\x12\x12\n\nTestDevice\x18\x08 \x01(\r\"O\n\x0fWvSecurityLevel\x12\x15\n\x11LEVEL_UNSPECIFIED\x10\x00\x12\x0b\n\x07LEVEL_1\x10\x01\x12\x0b\n\x07LEVEL_2\x10\x02\x12\x0b\n\x07LEVEL_3\x10\x03\"\x15\n\x13ProvisioningOptions\"\x15\n\x13ProvisioningRequest\"\x16\n\x14ProvisioningResponse\"i\n\x11RemoteAttestation\x12\x33\n\x0b\x43\x65rtificate\x18\x01 \x01(\x0b\x32\x1e.EncryptedClientIdentification\x12\x0c\n\x04Salt\x18\x02 \x01(\t\x12\x11\n\tSignature\x18\x03 \x01(\t\"\r\n\x0bSessionInit\"\x0e\n\x0cSessionState\"\x1d\n\x1bSignedCertificateStatusList\"\x86\x01\n\x17SignedDeviceCertificate\x12.\n\x12_DeviceCertificate\x18\x01 \x01(\x0b\x32\x12.DeviceCertificate\x12\x11\n\tSignature\x18\x02 \x01(\x0c\x12(\n\x06Signer\x18\x03 \x01(\x0b\x32\x18.SignedDeviceCertificate\"\x1b\n\x19SignedProvisioningMessage\"\xb0\x02\n\rSignedMessage\x12(\n\x04Type\x18\x01 \x01(\x0e\x32\x1a.SignedMessage.MessageType\x12\x0b\n\x03Msg\x18\x02 \x01(\x0c\x12\x11\n\tSignature\x18\x03 \x01(\x0c\x12\x12\n\nSessionKey\x18\x04 \x01(\x0c\x12-\n\x11RemoteAttestation\x18\x05 \x01(\x0b\x32\x12.RemoteAttestation\"\x91\x01\n\x0bMessageType\x12\x12\n\x0e\x44UMMY_MSG_TYPE\x10\x00\x12\x13\n\x0fLICENSE_REQUEST\x10\x01\x12\x0b\n\x07LICENSE\x10\x02\x12\x12\n\x0e\x45RROR_RESPONSE\x10\x03\x12\x1f\n\x1bSERVICE_CERTIFICATE_REQUEST\x10\x04\x12\x17\n\x13SERVICE_CERTIFICATE\x10\x05\"\xc5\x02\n\x12WidevineCencHeader\x12\x30\n\talgorithm\x18\x01 \x01(\x0e\x32\x1d.WidevineCencHeader.Algorithm\x12\x0e\n\x06key_id\x18\x02 \x03(\x0c\x12\x10\n\x08provider\x18\x03 \x01(\t\x12\x12\n\ncontent_id\x18\x04 \x01(\x0c\x12\x1d\n\x15track_type_deprecated\x18\x05 \x01(\t\x12\x0e\n\x06policy\x18\x06 \x01(\t\x12\x1b\n\x13\x63rypto_period_index\x18\x07 \x01(\r\x12\x17\n\x0fgrouped_license\x18\x08 \x01(\x0c\x12\x19\n\x11protection_scheme\x18\t \x01(\r\x12\x1d\n\x15\x63rypto_period_seconds\x18\n \x01(\r\"(\n\tAlgorithm\x12\x0f\n\x0bUNENCRYPTED\x10\x00\x12\n\n\x06\x41\x45SCTR\x10\x01\"\xcf\x02\n\x14SignedLicenseRequest\x12/\n\x04Type\x18\x01 \x01(\x0e\x32!.SignedLicenseRequest.MessageType\x12\x1c\n\x03Msg\x18\x02 \x01(\x0b\x32\x0f.LicenseRequest\x12\x11\n\tSignature\x18\x03 \x01(\x0c\x12\x12\n\nSessionKey\x18\x04 \x01(\x0c\x12-\n\x11RemoteAttestation\x18\x05 \x01(\x0b\x32\x12.RemoteAttestation\"\x91\x01\n\x0bMessageType\x12\x12\n\x0e\x44UMMY_MSG_TYPE\x10\x00\x12\x13\n\x0fLICENSE_REQUEST\x10\x01\x12\x0b\n\x07LICENSE\x10\x02\x12\x12\n\x0e\x45RROR_RESPONSE\x10\x03\x12\x1f\n\x1bSERVICE_CERTIFICATE_REQUEST\x10\x04\x12\x17\n\x13SERVICE_CERTIFICATE\x10\x05\"\xba\x02\n\rSignedLicense\x12(\n\x04Type\x18\x01 \x01(\x0e\x32\x1a.SignedLicense.MessageType\x12\x15\n\x03Msg\x18\x02 \x01(\x0b\x32\x08.License\x12\x11\n\tSignature\x18\x03 \x01(\x0c\x12\x12\n\nSessionKey\x18\x04 \x01(\x0c\x12-\n\x11RemoteAttestation\x18\x05 \x01(\x0b\x32\x12.RemoteAttestation\"\x91\x01\n\x0bMessageType\x12\x12\n\x0e\x44UMMY_MSG_TYPE\x10\x00\x12\x13\n\x0fLICENSE_REQUEST\x10\x01\x12\x0b\n\x07LICENSE\x10\x02\x12\x12\n\x0e\x45RROR_RESPONSE\x10\x03\x12\x1f\n\x1bSERVICE_CERTIFICATE_REQUEST\x10\x04\x12\x17\n\x13SERVICE_CERTIFICATE\x10\x05*$\n\x0bLicenseType\x12\x08\n\x04ZERO\x10\x00\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x01*)\n\x0fProtocolVersion\x12\t\n\x05\x44UMMY\x10\x00\x12\x0b\n\x07\x43URRENT\x10\x15\x62\x06proto3')

_LICENSETYPE = DESCRIPTOR.enum_types_by_name['LicenseType']
LicenseType = enum_type_wrapper.EnumTypeWrapper(_LICENSETYPE)
_PROTOCOLVERSION = DESCRIPTOR.enum_types_by_name['ProtocolVersion']
ProtocolVersion = enum_type_wrapper.EnumTypeWrapper(_PROTOCOLVERSION)
ZERO = 0
DEFAULT = 1
DUMMY = 0
CURRENT = 21

_CLIENTIDENTIFICATION = DESCRIPTOR.message_types_by_name['ClientIdentification']
_CLIENTIDENTIFICATION_NAMEVALUE = _CLIENTIDENTIFICATION.nested_types_by_name['NameValue']
_CLIENTIDENTIFICATION_CLIENTCAPABILITIES = _CLIENTIDENTIFICATION.nested_types_by_name['ClientCapabilities']
_DEVICECERTIFICATE = DESCRIPTOR.message_types_by_name['DeviceCertificate']
_DEVICECERTIFICATESTATUS = DESCRIPTOR.message_types_by_name['DeviceCertificateStatus']
_DEVICECERTIFICATESTATUSLIST = DESCRIPTOR.message_types_by_name['DeviceCertificateStatusList']
_ENCRYPTEDCLIENTIDENTIFICATION = DESCRIPTOR.message_types_by_name['EncryptedClientIdentification']
_LICENSEIDENTIFICATION = DESCRIPTOR.message_types_by_name['LicenseIdentification']
_LICENSE = DESCRIPTOR.message_types_by_name['License']
_LICENSE_POLICY = _LICENSE.nested_types_by_name['Policy']
_LICENSE_KEYCONTAINER = _LICENSE.nested_types_by_name['KeyContainer']
_LICENSE_KEYCONTAINER_OUTPUTPROTECTION = _LICENSE_KEYCONTAINER.nested_types_by_name['OutputProtection']
_LICENSE_KEYCONTAINER_KEYCONTROL = _LICENSE_KEYCONTAINER.nested_types_by_name['KeyControl']
_LICENSE_KEYCONTAINER_OPERATORSESSIONKEYPERMISSIONS = _LICENSE_KEYCONTAINER.nested_types_by_name[
    'OperatorSessionKeyPermissions']
_LICENSE_KEYCONTAINER_VIDEORESOLUTIONCONSTRAINT = _LICENSE_KEYCONTAINER.nested_types_by_name[
    'VideoResolutionConstraint']
_LICENSEERROR = DESCRIPTOR.message_types_by_name['LicenseError']
_LICENSEREQUEST = DESCRIPTOR.message_types_by_name['LicenseRequest']
_LICENSEREQUEST_CONTENTIDENTIFICATION = _LICENSEREQUEST.nested_types_by_name['ContentIdentification']
_LICENSEREQUEST_CONTENTIDENTIFICATION_CENC = _LICENSEREQUEST_CONTENTIDENTIFICATION.nested_types_by_name['CENC']
_LICENSEREQUEST_CONTENTIDENTIFICATION_WEBM = _LICENSEREQUEST_CONTENTIDENTIFICATION.nested_types_by_name['WebM']
_LICENSEREQUEST_CONTENTIDENTIFICATION_EXISTINGLICENSE = _LICENSEREQUEST_CONTENTIDENTIFICATION.nested_types_by_name[
    'ExistingLicense']
_PROVISIONEDDEVICEINFO = DESCRIPTOR.message_types_by_name['ProvisionedDeviceInfo']
_PROVISIONINGOPTIONS = DESCRIPTOR.message_types_by_name['ProvisioningOptions']
_PROVISIONINGREQUEST = DESCRIPTOR.message_types_by_name['ProvisioningRequest']
_PROVISIONINGRESPONSE = DESCRIPTOR.message_types_by_name['ProvisioningResponse']
_REMOTEATTESTATION = DESCRIPTOR.message_types_by_name['RemoteAttestation']
_SESSIONINIT = DESCRIPTOR.message_types_by_name['SessionInit']
_SESSIONSTATE = DESCRIPTOR.message_types_by_name['SessionState']
_SIGNEDCERTIFICATESTATUSLIST = DESCRIPTOR.message_types_by_name['SignedCertificateStatusList']
_SIGNEDDEVICECERTIFICATE = DESCRIPTOR.message_types_by_name['SignedDeviceCertificate']
_SIGNEDPROVISIONINGMESSAGE = DESCRIPTOR.message_types_by_name['SignedProvisioningMessage']
_SIGNEDMESSAGE = DESCRIPTOR.message_types_by_name['SignedMessage']
_WIDEVINECENCHEADER = DESCRIPTOR.message_types_by_name['WidevineCencHeader']
_SIGNEDLICENSEREQUEST = DESCRIPTOR.message_types_by_name['SignedLicenseRequest']
_SIGNEDLICENSE = DESCRIPTOR.message_types_by_name['SignedLicense']
_CLIENTIDENTIFICATION_CLIENTCAPABILITIES_HDCPVERSION = _CLIENTIDENTIFICATION_CLIENTCAPABILITIES.enum_types_by_name[
    'HdcpVersion']
_CLIENTIDENTIFICATION_TOKENTYPE = _CLIENTIDENTIFICATION.enum_types_by_name['TokenType']
_DEVICECERTIFICATE_CERTIFICATETYPE = _DEVICECERTIFICATE.enum_types_by_name['CertificateType']
_DEVICECERTIFICATESTATUS_CERTIFICATESTATUS = _DEVICECERTIFICATESTATUS.enum_types_by_name['CertificateStatus']
_LICENSE_KEYCONTAINER_OUTPUTPROTECTION_CGMS = _LICENSE_KEYCONTAINER_OUTPUTPROTECTION.enum_types_by_name['CGMS']
_LICENSE_KEYCONTAINER_KEYTYPE = _LICENSE_KEYCONTAINER.enum_types_by_name['KeyType']
_LICENSE_KEYCONTAINER_SECURITYLEVEL = _LICENSE_KEYCONTAINER.enum_types_by_name['SecurityLevel']
_LICENSEERROR_ERROR = _LICENSEERROR.enum_types_by_name['Error']
_LICENSEREQUEST_REQUESTTYPE = _LICENSEREQUEST.enum_types_by_name['RequestType']
_PROVISIONEDDEVICEINFO_WVSECURITYLEVEL = _PROVISIONEDDEVICEINFO.enum_types_by_name['WvSecurityLevel']
_SIGNEDMESSAGE_MESSAGETYPE = _SIGNEDMESSAGE.enum_types_by_name['MessageType']
_WIDEVINECENCHEADER_ALGORITHM = _WIDEVINECENCHEADER.enum_types_by_name['Algorithm']
_SIGNEDLICENSEREQUEST_MESSAGETYPE = _SIGNEDLICENSEREQUEST.enum_types_by_name['MessageType']
_SIGNEDLICENSE_MESSAGETYPE = _SIGNEDLICENSE.enum_types_by_name['MessageType']
ClientIdentification = _reflection.GeneratedProtocolMessageType('ClientIdentification', (_message.Message,), {

    'NameValue': _reflection.GeneratedProtocolMessageType('NameValue', (_message.Message,), {
        'DESCRIPTOR': _CLIENTIDENTIFICATION_NAMEVALUE,
        '__module__': 'wv_proto4_pb2'
        # @@protoc_insertion_point(class_scope:ClientIdentification.NameValue)
    })
    ,

    'ClientCapabilities': _reflection.GeneratedProtocolMessageType('ClientCapabilities', (_message.Message,), {
        'DESCRIPTOR': _CLIENTIDENTIFICATION_CLIENTCAPABILITIES,
        '__module__': 'wv_proto4_pb2'
        # @@protoc_insertion_point(class_scope:ClientIdentification.ClientCapabilities)
    })
    ,
    'DESCRIPTOR': _CLIENTIDENTIFICATION,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:ClientIdentification)
})
_sym_db.RegisterMessage(ClientIdentification)
_sym_db.RegisterMessage(ClientIdentification.NameValue)
_sym_db.RegisterMessage(ClientIdentification.ClientCapabilities)

DeviceCertificate = _reflection.GeneratedProtocolMessageType('DeviceCertificate', (_message.Message,), {
    'DESCRIPTOR': _DEVICECERTIFICATE,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:DeviceCertificate)
})
_sym_db.RegisterMessage(DeviceCertificate)

DeviceCertificateStatus = _reflection.GeneratedProtocolMessageType('DeviceCertificateStatus', (_message.Message,), {
    'DESCRIPTOR': _DEVICECERTIFICATESTATUS,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:DeviceCertificateStatus)
})
_sym_db.RegisterMessage(DeviceCertificateStatus)

DeviceCertificateStatusList = _reflection.GeneratedProtocolMessageType('DeviceCertificateStatusList',
                                                                       (_message.Message,), {
                                                                           'DESCRIPTOR': _DEVICECERTIFICATESTATUSLIST,
                                                                           '__module__': 'wv_proto4_pb2'
                                                                           # @@protoc_insertion_point(class_scope:DeviceCertificateStatusList)
                                                                       })
_sym_db.RegisterMessage(DeviceCertificateStatusList)

EncryptedClientIdentification = _reflection.GeneratedProtocolMessageType('EncryptedClientIdentification',
                                                                         (_message.Message,), {
                                                                             'DESCRIPTOR': _ENCRYPTEDCLIENTIDENTIFICATION,
                                                                             '__module__': 'wv_proto4_pb2'
                                                                             # @@protoc_insertion_point(class_scope:EncryptedClientIdentification)
                                                                         })
_sym_db.RegisterMessage(EncryptedClientIdentification)

LicenseIdentification = _reflection.GeneratedProtocolMessageType('LicenseIdentification', (_message.Message,), {
    'DESCRIPTOR': _LICENSEIDENTIFICATION,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:LicenseIdentification)
})
_sym_db.RegisterMessage(LicenseIdentification)

License = _reflection.GeneratedProtocolMessageType('License', (_message.Message,), {

    'Policy': _reflection.GeneratedProtocolMessageType('Policy', (_message.Message,), {
        'DESCRIPTOR': _LICENSE_POLICY,
        '__module__': 'wv_proto4_pb2'
        # @@protoc_insertion_point(class_scope:License.Policy)
    })
    ,

    'KeyContainer': _reflection.GeneratedProtocolMessageType('KeyContainer', (_message.Message,), {

        'OutputProtection': _reflection.GeneratedProtocolMessageType('OutputProtection', (_message.Message,), {
            'DESCRIPTOR': _LICENSE_KEYCONTAINER_OUTPUTPROTECTION,
            '__module__': 'wv_proto4_pb2'
            # @@protoc_insertion_point(class_scope:License.KeyContainer.OutputProtection)
        })
        ,

        'KeyControl': _reflection.GeneratedProtocolMessageType('KeyControl', (_message.Message,), {
            'DESCRIPTOR': _LICENSE_KEYCONTAINER_KEYCONTROL,
            '__module__': 'wv_proto4_pb2'
            # @@protoc_insertion_point(class_scope:License.KeyContainer.KeyControl)
        })
        ,

        'OperatorSessionKeyPermissions': _reflection.GeneratedProtocolMessageType('OperatorSessionKeyPermissions',
                                                                                  (_message.Message,), {
                                                                                      'DESCRIPTOR': _LICENSE_KEYCONTAINER_OPERATORSESSIONKEYPERMISSIONS,
                                                                                      '__module__': 'wv_proto4_pb2'
                                                                                      # @@protoc_insertion_point(class_scope:License.KeyContainer.OperatorSessionKeyPermissions)
                                                                                  })
        ,

        'VideoResolutionConstraint': _reflection.GeneratedProtocolMessageType('VideoResolutionConstraint',
                                                                              (_message.Message,), {
                                                                                  'DESCRIPTOR': _LICENSE_KEYCONTAINER_VIDEORESOLUTIONCONSTRAINT,
                                                                                  '__module__': 'wv_proto4_pb2'
                                                                                  # @@protoc_insertion_point(class_scope:License.KeyContainer.VideoResolutionConstraint)
                                                                              })
        ,
        'DESCRIPTOR': _LICENSE_KEYCONTAINER,
        '__module__': 'wv_proto4_pb2'
        # @@protoc_insertion_point(class_scope:License.KeyContainer)
    })
    ,
    'DESCRIPTOR': _LICENSE,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:License)
})
_sym_db.RegisterMessage(License)
_sym_db.RegisterMessage(License.Policy)
_sym_db.RegisterMessage(License.KeyContainer)
_sym_db.RegisterMessage(License.KeyContainer.OutputProtection)
_sym_db.RegisterMessage(License.KeyContainer.KeyControl)
_sym_db.RegisterMessage(License.KeyContainer.OperatorSessionKeyPermissions)
_sym_db.RegisterMessage(License.KeyContainer.VideoResolutionConstraint)

LicenseError = _reflection.GeneratedProtocolMessageType('LicenseError', (_message.Message,), {
    'DESCRIPTOR': _LICENSEERROR,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:LicenseError)
})
_sym_db.RegisterMessage(LicenseError)

LicenseRequest = _reflection.GeneratedProtocolMessageType('LicenseRequest', (_message.Message,), {

    'ContentIdentification': _reflection.GeneratedProtocolMessageType('ContentIdentification', (_message.Message,), {

        'CENC': _reflection.GeneratedProtocolMessageType('CENC', (_message.Message,), {
            'DESCRIPTOR': _LICENSEREQUEST_CONTENTIDENTIFICATION_CENC,
            '__module__': 'wv_proto4_pb2'
            # @@protoc_insertion_point(class_scope:LicenseRequest.ContentIdentification.CENC)
        })
        ,

        'WebM': _reflection.GeneratedProtocolMessageType('WebM', (_message.Message,), {
            'DESCRIPTOR': _LICENSEREQUEST_CONTENTIDENTIFICATION_WEBM,
            '__module__': 'wv_proto4_pb2'
            # @@protoc_insertion_point(class_scope:LicenseRequest.ContentIdentification.WebM)
        })
        ,

        'ExistingLicense': _reflection.GeneratedProtocolMessageType('ExistingLicense', (_message.Message,), {
            'DESCRIPTOR': _LICENSEREQUEST_CONTENTIDENTIFICATION_EXISTINGLICENSE,
            '__module__': 'wv_proto4_pb2'
            # @@protoc_insertion_point(class_scope:LicenseRequest.ContentIdentification.ExistingLicense)
        })
        ,
        'DESCRIPTOR': _LICENSEREQUEST_CONTENTIDENTIFICATION,
        '__module__': 'wv_proto4_pb2'
        # @@protoc_insertion_point(class_scope:LicenseRequest.ContentIdentification)
    })
    ,
    'DESCRIPTOR': _LICENSEREQUEST,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:LicenseRequest)
})
_sym_db.RegisterMessage(LicenseRequest)
_sym_db.RegisterMessage(LicenseRequest.ContentIdentification)
_sym_db.RegisterMessage(LicenseRequest.ContentIdentification.CENC)
_sym_db.RegisterMessage(LicenseRequest.ContentIdentification.WebM)
_sym_db.RegisterMessage(LicenseRequest.ContentIdentification.ExistingLicense)

ProvisionedDeviceInfo = _reflection.GeneratedProtocolMessageType('ProvisionedDeviceInfo', (_message.Message,), {
    'DESCRIPTOR': _PROVISIONEDDEVICEINFO,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:ProvisionedDeviceInfo)
})
_sym_db.RegisterMessage(ProvisionedDeviceInfo)

ProvisioningOptions = _reflection.GeneratedProtocolMessageType('ProvisioningOptions', (_message.Message,), {
    'DESCRIPTOR': _PROVISIONINGOPTIONS,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:ProvisioningOptions)
})
_sym_db.RegisterMessage(ProvisioningOptions)

ProvisioningRequest = _reflection.GeneratedProtocolMessageType('ProvisioningRequest', (_message.Message,), {
    'DESCRIPTOR': _PROVISIONINGREQUEST,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:ProvisioningRequest)
})
_sym_db.RegisterMessage(ProvisioningRequest)

ProvisioningResponse = _reflection.GeneratedProtocolMessageType('ProvisioningResponse', (_message.Message,), {
    'DESCRIPTOR': _PROVISIONINGRESPONSE,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:ProvisioningResponse)
})
_sym_db.RegisterMessage(ProvisioningResponse)

RemoteAttestation = _reflection.GeneratedProtocolMessageType('RemoteAttestation', (_message.Message,), {
    'DESCRIPTOR': _REMOTEATTESTATION,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:RemoteAttestation)
})
_sym_db.RegisterMessage(RemoteAttestation)

SessionInit = _reflection.GeneratedProtocolMessageType('SessionInit', (_message.Message,), {
    'DESCRIPTOR': _SESSIONINIT,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:SessionInit)
})
_sym_db.RegisterMessage(SessionInit)

SessionState = _reflection.GeneratedProtocolMessageType('SessionState', (_message.Message,), {
    'DESCRIPTOR': _SESSIONSTATE,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:SessionState)
})
_sym_db.RegisterMessage(SessionState)

SignedCertificateStatusList = _reflection.GeneratedProtocolMessageType('SignedCertificateStatusList',
                                                                       (_message.Message,), {
                                                                           'DESCRIPTOR': _SIGNEDCERTIFICATESTATUSLIST,
                                                                           '__module__': 'wv_proto4_pb2'
                                                                           # @@protoc_insertion_point(class_scope:SignedCertificateStatusList)
                                                                       })
_sym_db.RegisterMessage(SignedCertificateStatusList)

SignedDeviceCertificate = _reflection.GeneratedProtocolMessageType('SignedDeviceCertificate', (_message.Message,), {
    'DESCRIPTOR': _SIGNEDDEVICECERTIFICATE,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:SignedDeviceCertificate)
})
_sym_db.RegisterMessage(SignedDeviceCertificate)

SignedProvisioningMessage = _reflection.GeneratedProtocolMessageType('SignedProvisioningMessage', (_message.Message,), {
    'DESCRIPTOR': _SIGNEDPROVISIONINGMESSAGE,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:SignedProvisioningMessage)
})
_sym_db.RegisterMessage(SignedProvisioningMessage)

SignedMessage = _reflection.GeneratedProtocolMessageType('SignedMessage', (_message.Message,), {
    'DESCRIPTOR': _SIGNEDMESSAGE,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:SignedMessage)
})
_sym_db.RegisterMessage(SignedMessage)

WidevineCencHeader = _reflection.GeneratedProtocolMessageType('WidevineCencHeader', (_message.Message,), {
    'DESCRIPTOR': _WIDEVINECENCHEADER,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:WidevineCencHeader)
})
_sym_db.RegisterMessage(WidevineCencHeader)

SignedLicenseRequest = _reflection.GeneratedProtocolMessageType('SignedLicenseRequest', (_message.Message,), {
    'DESCRIPTOR': _SIGNEDLICENSEREQUEST,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:SignedLicenseRequest)
})
_sym_db.RegisterMessage(SignedLicenseRequest)

SignedLicense = _reflection.GeneratedProtocolMessageType('SignedLicense', (_message.Message,), {
    'DESCRIPTOR': _SIGNEDLICENSE,
    '__module__': 'wv_proto4_pb2'
    # @@protoc_insertion_point(class_scope:SignedLicense)
})
_sym_db.RegisterMessage(SignedLicense)

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    _LICENSETYPE._serialized_start = 6713
    _LICENSETYPE._serialized_end = 6749
    _PROTOCOLVERSION._serialized_start = 6751
    _PROTOCOLVERSION._serialized_end = 6792
    _CLIENTIDENTIFICATION._serialized_start = 20
    _CLIENTIDENTIFICATION._serialized_end = 729
    _CLIENTIDENTIFICATION_NAMEVALUE._serialized_start = 309
    _CLIENTIDENTIFICATION_NAMEVALUE._serialized_end = 349
    _CLIENTIDENTIFICATION_CLIENTCAPABILITIES._serialized_start = 352
    _CLIENTIDENTIFICATION_CLIENTCAPABILITIES._serialized_end = 644
    _CLIENTIDENTIFICATION_CLIENTCAPABILITIES_HDCPVERSION._serialized_start = 560
    _CLIENTIDENTIFICATION_CLIENTCAPABILITIES_HDCPVERSION._serialized_end = 644
    _CLIENTIDENTIFICATION_TOKENTYPE._serialized_start = 646
    _CLIENTIDENTIFICATION_TOKENTYPE._serialized_end = 729
    _DEVICECERTIFICATE._serialized_start = 732
    _DEVICECERTIFICATE._serialized_end = 1015
    _DEVICECERTIFICATE_CERTIFICATETYPE._serialized_start = 940
    _DEVICECERTIFICATE_CERTIFICATETYPE._serialized_end = 1015
    _DEVICECERTIFICATESTATUS._serialized_start = 1018
    _DEVICECERTIFICATESTATUS._serialized_end = 1214
    _DEVICECERTIFICATESTATUS_CERTIFICATESTATUS._serialized_start = 1171
    _DEVICECERTIFICATESTATUS_CERTIFICATESTATUS._serialized_end = 1214
    _DEVICECERTIFICATESTATUSLIST._serialized_start = 1216
    _DEVICECERTIFICATESTATUSLIST._serialized_end = 1327
    _ENCRYPTEDCLIENTIDENTIFICATION._serialized_start = 1330
    _ENCRYPTEDCLIENTIDENTIFICATION._serialized_end = 1505
    _LICENSEIDENTIFICATION._serialized_start = 1508
    _LICENSEIDENTIFICATION._serialized_end = 1664
    _LICENSE._serialized_start = 1667
    _LICENSE._serialized_end = 3581
    _LICENSE_POLICY._serialized_start = 1931
    _LICENSE_POLICY._serialized_end = 2271
    _LICENSE_KEYCONTAINER._serialized_start = 2274
    _LICENSE_KEYCONTAINER._serialized_end = 3581
    _LICENSE_KEYCONTAINER_OUTPUTPROTECTION._serialized_start = 2795
    _LICENSE_KEYCONTAINER_OUTPUTPROTECTION._serialized_end = 3014
    _LICENSE_KEYCONTAINER_OUTPUTPROTECTION_CGMS._serialized_start = 2947
    _LICENSE_KEYCONTAINER_OUTPUTPROTECTION_CGMS._serialized_end = 3014
    _LICENSE_KEYCONTAINER_KEYCONTROL._serialized_start = 3016
    _LICENSE_KEYCONTAINER_KEYCONTROL._serialized_end = 3065
    _LICENSE_KEYCONTAINER_OPERATORSESSIONKEYPERMISSIONS._serialized_start = 3067
    _LICENSE_KEYCONTAINER_OPERATORSESSIONKEYPERMISSIONS._serialized_end = 3191
    _LICENSE_KEYCONTAINER_VIDEORESOLUTIONCONSTRAINT._serialized_start = 3194
    _LICENSE_KEYCONTAINER_VIDEORESOLUTIONCONSTRAINT._serialized_end = 3347
    _LICENSE_KEYCONTAINER_KEYTYPE._serialized_start = 3349
    _LICENSE_KEYCONTAINER_KEYTYPE._serialized_end = 3439
    _LICENSE_KEYCONTAINER_SECURITYLEVEL._serialized_start = 3442
    _LICENSE_KEYCONTAINER_SECURITYLEVEL._serialized_end = 3581
    _LICENSEERROR._serialized_start = 3584
    _LICENSEERROR._serialized_end = 3756
    _LICENSEERROR_ERROR._serialized_start = 3640
    _LICENSEERROR_ERROR._serialized_end = 3756
    _LICENSEREQUEST._serialized_start = 3759
    _LICENSEREQUEST._serialized_end = 4719
    _LICENSEREQUEST_CONTENTIDENTIFICATION._serialized_start = 4103
    _LICENSEREQUEST_CONTENTIDENTIFICATION._serialized_end = 4649
    _LICENSEREQUEST_CONTENTIDENTIFICATION_CENC._serialized_start = 4320
    _LICENSEREQUEST_CONTENTIDENTIFICATION_CENC._serialized_end = 4415
    _LICENSEREQUEST_CONTENTIDENTIFICATION_WEBM._serialized_start = 4417
    _LICENSEREQUEST_CONTENTIDENTIFICATION_WEBM._serialized_end = 4493
    _LICENSEREQUEST_CONTENTIDENTIFICATION_EXISTINGLICENSE._serialized_start = 4496
    _LICENSEREQUEST_CONTENTIDENTIFICATION_EXISTINGLICENSE._serialized_end = 4649
    _LICENSEREQUEST_REQUESTTYPE._serialized_start = 4651
    _LICENSEREQUEST_REQUESTTYPE._serialized_end = 4719
    _PROVISIONEDDEVICEINFO._serialized_start = 4722
    _PROVISIONEDDEVICEINFO._serialized_end = 5016
    _PROVISIONEDDEVICEINFO_WVSECURITYLEVEL._serialized_start = 4937
    _PROVISIONEDDEVICEINFO_WVSECURITYLEVEL._serialized_end = 5016
    _PROVISIONINGOPTIONS._serialized_start = 5018
    _PROVISIONINGOPTIONS._serialized_end = 5039
    _PROVISIONINGREQUEST._serialized_start = 5041
    _PROVISIONINGREQUEST._serialized_end = 5062
    _PROVISIONINGRESPONSE._serialized_start = 5064
    _PROVISIONINGRESPONSE._serialized_end = 5086
    _REMOTEATTESTATION._serialized_start = 5088
    _REMOTEATTESTATION._serialized_end = 5193
    _SESSIONINIT._serialized_start = 5195
    _SESSIONINIT._serialized_end = 5208
    _SESSIONSTATE._serialized_start = 5210
    _SESSIONSTATE._serialized_end = 5224
    _SIGNEDCERTIFICATESTATUSLIST._serialized_start = 5226
    _SIGNEDCERTIFICATESTATUSLIST._serialized_end = 5255
    _SIGNEDDEVICECERTIFICATE._serialized_start = 5258
    _SIGNEDDEVICECERTIFICATE._serialized_end = 5392
    _SIGNEDPROVISIONINGMESSAGE._serialized_start = 5394
    _SIGNEDPROVISIONINGMESSAGE._serialized_end = 5421
    _SIGNEDMESSAGE._serialized_start = 5424
    _SIGNEDMESSAGE._serialized_end = 5728
    _SIGNEDMESSAGE_MESSAGETYPE._serialized_start = 5583
    _SIGNEDMESSAGE_MESSAGETYPE._serialized_end = 5728
    _WIDEVINECENCHEADER._serialized_start = 5731
    _WIDEVINECENCHEADER._serialized_end = 6056
    _WIDEVINECENCHEADER_ALGORITHM._serialized_start = 6016
    _WIDEVINECENCHEADER_ALGORITHM._serialized_end = 6056
    _SIGNEDLICENSEREQUEST._serialized_start = 6059
    _SIGNEDLICENSEREQUEST._serialized_end = 6394
    _SIGNEDLICENSEREQUEST_MESSAGETYPE._serialized_start = 5583
    _SIGNEDLICENSEREQUEST_MESSAGETYPE._serialized_end = 5728
    _SIGNEDLICENSE._serialized_start = 6397
    _SIGNEDLICENSE._serialized_end = 6711
    _SIGNEDLICENSE_MESSAGETYPE._serialized_start = 5583
    _SIGNEDLICENSE_MESSAGETYPE._serialized_end = 5728
# @@protoc_insertion_point(module_scope)
